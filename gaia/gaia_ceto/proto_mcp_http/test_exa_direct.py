#!/usr/bin/env python3
"""
Test Exa MCP Server Direct Connection

This script tests the exa-mcp server connection directly to debug any issues.
"""

import asyncio
import logging
import os
import sys
from contextlib import AsyncExitStack

# Import MCP stdio client
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


async def test_exa_direct():
    """Test direct connection to exa-mcp server."""
    
    # Check API key
    api_key = os.getenv('EXA_API_KEY')
    if not api_key:
        print("ERROR: EXA_API_KEY environment variable not set")
        return
    
    print(f"Using EXA_API_KEY: {api_key[:10]}...")
    
    async with AsyncExitStack() as exit_stack:
        try:
            # Prepare environment
            env = os.environ.copy()
            env['EXA_API_KEY'] = api_key
            
            # Create server parameters
            server_params = StdioServerParameters(
                command="npx",
                args=["-y", "exa-mcp"],
                env=env
            )
            
            print("Spawning exa-mcp server...")
            
            # Create stdio client and session
            stdio_transport = await exit_stack.enter_async_context(stdio_client(server_params))
            read_stream, write_stream = stdio_transport
            session = await exit_stack.enter_async_context(
                ClientSession(read_stream, write_stream)
            )
            
            print("Initializing session...")
            await session.initialize()
            
            print("Listing available tools...")
            tools_result = await session.list_tools()
            
            print(f"Available tools: {[tool.name for tool in tools_result.tools]}")
            
            for tool in tools_result.tools:
                print(f"  - {tool.name}: {tool.description}")
                print(f"    Schema: {tool.inputSchema}")
            
            # Test the exa_search tool
            print("\nTesting exa_search tool...")
            
            test_queries = [
                {"query": "artificial intelligence"},
                {"query": "machine learning", "num_results": 3},
                {"query": "python programming", "num_results": 2, "type": "neural"}
            ]
            
            for i, test_args in enumerate(test_queries):
                print(f"\nTest {i+1}: {test_args}")
                try:
                    result = await session.call_tool("exa_search", test_args)
                    print(f"Result type: {type(result)}")
                    print(f"Result attributes: {[attr for attr in dir(result) if not attr.startswith('_')]}")
                    
                    if hasattr(result, 'isError'):
                        print(f"Is error: {result.isError}")
                    
                    if hasattr(result, 'content'):
                        print(f"Content type: {type(result.content)}")
                        print(f"Content: {result.content}")
                        
                        if isinstance(result.content, list):
                            for j, item in enumerate(result.content):
                                print(f"  Content item {j}: {type(item)}")
                                if hasattr(item, 'text'):
                                    print(f"    Text: {item.text[:200]}...")
                                elif hasattr(item, 'data'):
                                    print(f"    Data: {item.data}")
                                else:
                                    print(f"    Raw: {item}")
                    
                    print("✓ Tool call successful")
                    
                except Exception as e:
                    print(f"✗ Tool call failed: {e}")
                    import traceback
                    traceback.print_exc()
            
        except Exception as e:
            print(f"Error: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_exa_direct())

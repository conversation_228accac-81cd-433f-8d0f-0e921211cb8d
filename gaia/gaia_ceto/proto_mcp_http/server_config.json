{"description": "Enhanced MCP Server Configuration (Augment Pattern)", "mcpServers": {"firecrawl-mcp": {"enabled": false, "command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "{FIRECRAWL_API_KEY}"}, "namespace": "fc", "description": "Firecrawl MCP server spawned via npx"}, "exa-mcp": {"enabled": false, "command": "npx", "args": ["-y", "exa-mcp"], "env": {"EXA_API_KEY": "{EXA_API_KEY}"}, "namespace": "exa", "description": "Embedding-based web search with Exa"}, "brave-search-mcp": {"enabled": true, "command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_SEARCH_API_KEY": "{BRAVE_SEARCH_API_KEY}"}, "namespace": "search", "description": "Brave Search API for web and local search."}}, "chat_term_usage": {"description": "Connect chat_term to this enhanced server", "command": "python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp-http --mcp-http-server http://localhost:9000/mcp", "available_tools": ["echostring (local)", "echostring_table (local)", "long_task (local)", "server_status (local)", "list_all_tools (local)", "third_party_health (local)", "fc_firecrawl_scrape (delegated)", "fs_read_file (delegated)"]}}
#!/usr/bin/env python3
"""
Enhanced debug script for Firecrawl MCP integration issues.
"""

import asyncio
import os
import sys
import traceback
import logging

# Add the parent directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from gaia.gaia_ceto.proto_mcp.mcp_sse_clientlib import MCPClientLib

# Enable debug logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def debug_callback(level: str, message: str, data=None):
    """Enhanced debug callback with more details."""
    print(f"[{level.upper()}] {message}")
    if data:
        print(f"  Data: {data}")
        print(f"  Data type: {type(data)}")
        if hasattr(data, '__dict__'):
            print(f"  Data attributes: {data.__dict__}")

async def main():
    """Test Firecrawl MCP connection and tool calls."""
    
    # Get Firecrawl API key from environment
    firecrawl_api_key = os.getenv('FIRECRAWL_API_KEY')
    if not firecrawl_api_key:
        print("❌ FIRECRAWL_API_KEY environment variable not set")
        return
    
    # Create Firecrawl MCP URL
    firecrawl_url = f"https://mcp.firecrawl.dev/{firecrawl_api_key}/sse"
    
    print(f"🔗 Connecting to Firecrawl MCP at: {firecrawl_url}")
    
    # Create client with enhanced debugging
    client = MCPClientLib(debug_callback=debug_callback)
    
    try:
        # Connect to Firecrawl
        print("Attempting connection...")
        success = await client.connect_to_server(firecrawl_url)
        
        if success:
            print('✅ Connection successful!')
            print(f'Available tools: {[tool["name"] for tool in client.available_tools]}')
            
            # Check session status
            print(f'Session object: {client.session}')
            print(f'Session type: {type(client.session)}')
            if hasattr(client.session, 'connected'):
                print(f'Session connected: {client.session.connected}')
            
            # Test with a simple URL first
            print('\n🧪 Testing firecrawl_scrape tool with example.com...')
            try:
                result = await client.call_tool(
                    tool_name='firecrawl_scrape',
                    tool_input={'url': 'https://example.com', 'formats': ['markdown']},
                    tool_call_id='debug_call_simple',
                    timeout=60.0
                )
                
                print(f'Simple test result success: {result.success}')
                if result.success:
                    content_preview = str(result.content)[:200] + '...' if len(str(result.content)) > 200 else str(result.content)
                    print(f'Content preview: {content_preview}')
                else:
                    print(f'Error: "{result.error}"')
                    print(f'Error type: {type(result.error)}')
                    print(f'Error repr: {repr(result.error)}')
                    print(f'Error length: {len(str(result.error)) if result.error else "None"}')
                    
            except Exception as e:
                print(f'Exception in simple test: {e}')
                print(f'Exception type: {type(e)}')
                print(f'Exception repr: {repr(e)}')
                print(f'Exception args: {e.args}')
                traceback.print_exc()
            
            # Test with the original failing URL
            print('\n🎯 Testing firecrawl_scrape tool with agfunder.com...')
            try:
                result = await client.call_tool(
                    tool_name='firecrawl_scrape',
                    tool_input={'url': 'https://agfunder.com', 'formats': ['markdown'], 'onlyMainContent': True},
                    tool_call_id='debug_call_agfunder',
                    timeout=120.0  # 2 minutes timeout
                )
                
                print(f'AgFunder test result success: {result.success}')
                if result.success:
                    content_preview = str(result.content)[:200] + '...' if len(str(result.content)) > 200 else str(result.content)
                    print(f'Content preview: {content_preview}')
                else:
                    print(f'Error: "{result.error}"')
                    print(f'Error type: {type(result.error)}')
                    print(f'Error repr: {repr(result.error)}')
                    print(f'Error length: {len(str(result.error)) if result.error else "None"}')
                    
            except Exception as e:
                print(f'Exception in AgFunder test: {e}')
                print(f'Exception type: {type(e)}')
                print(f'Exception repr: {repr(e)}')
                print(f'Exception args: {e.args}')
                traceback.print_exc()
                
        else:
            print('❌ Connection failed')
            
    except Exception as e:
        print(f'Top-level exception: {e}')
        print(f'Exception type: {type(e)}')
        print(f'Exception repr: {repr(e)}')
        print(f'Exception args: {e.args}')
        traceback.print_exc()
    finally:
        print("Cleaning up...")
        await client.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
